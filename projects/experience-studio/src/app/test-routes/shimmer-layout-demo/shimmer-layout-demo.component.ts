import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

interface LayoutConfig {
  key: string;
  title: string;
  class: string;
  elements: string[];
  description: string;
}

@Component({
  selector: 'app-shimmer-layout-demo',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './shimmer-layout-demo.component.html',
  styleUrls: ['./shimmer-layout-demo.component.scss']
})
export class ShimmerLayoutDemoComponent implements OnInit {

  selectedLayout = 'hb';
  isFullscreen = false;
  
  layouts: { [key: string]: LayoutConfig } = {
    'hb': {
      key: 'HB',
      title: 'Header + Body Layout',
      class: 'hb-layout',
      elements: ['header', 'body'],
      description: 'Simple two-section layout with header and main content area. Perfect for landing pages and simple applications.'
    },
    'hbf': {
      key: 'HBF',
      title: 'Header + Body + Footer Layout',
      class: 'hbf-layout',
      elements: ['header', 'body', 'footer'],
      description: 'Classic three-section layout with header, main content, and footer. Ideal for corporate websites and blogs.'
    },
    'hlsb': {
      key: 'HLSB',
      title: 'Header + Left Sidebar + Body Layout',
      class: 'hlsb-layout',
      elements: ['header', 'left-sidebar', 'body'],
      description: 'Layout with navigation sidebar on the left. Great for admin dashboards and content management systems.'
    },
    'hlsbf': {
      key: 'HLSBF',
      title: 'Header + Left Sidebar + Body + Footer Layout',
      class: 'hlsbf-layout',
      elements: ['header', 'left-sidebar', 'body', 'footer'],
      description: 'Complete layout with left navigation, header, content area, and footer. Perfect for full-featured applications.'
    },
    'hbrs': {
      key: 'HBRS',
      title: 'Header + Body + Right Sidebar Layout',
      class: 'hbrs-layout',
      elements: ['header', 'body', 'right-sidebar'],
      description: 'Layout with sidebar on the right for secondary content. Ideal for blogs with widgets or news sites.'
    },
    'hbrsf': {
      key: 'HBRSF',
      title: 'Header + Body + Right Sidebar + Footer Layout',
      class: 'hbrsf-layout',
      elements: ['header', 'body', 'right-sidebar', 'footer'],
      description: 'Complete layout with right sidebar for additional content and footer. Great for content-heavy websites.'
    },
    'hlsbrs': {
      key: 'HLSBRS',
      title: 'Header + Left + Body + Right Sidebar Layout',
      class: 'hlsbrs-layout',
      elements: ['header', 'left-sidebar', 'body', 'right-sidebar'],
      description: 'Dual sidebar layout with navigation on left and widgets on right. Perfect for complex applications.'
    },
    'hlsbrsf': {
      key: 'HLSBRSF',
      title: 'Header + Left + Body + Right + Footer Layout',
      class: 'hlsbrsf-layout',
      elements: ['header', 'left-sidebar', 'body', 'right-sidebar', 'footer'],
      description: 'Complete layout with dual sidebars and footer. Maximum content organization for enterprise applications.'
    }
  };

  constructor(private router: Router) { }

  ngOnInit(): void {
    console.log('Shimmer Layout Demo Component initialized');
  }

  /**
   * Select a layout and update the display
   */
  selectLayout(layoutKey: string): void {
    this.selectedLayout = layoutKey;
    console.log('Selected layout:', layoutKey, this.getCurrentLayout());
  }

  /**
   * Get the current layout configuration
   */
  getCurrentLayout(): LayoutConfig {
    return this.layouts[this.selectedLayout];
  }

  /**
   * Check if layout has left sidebar
   */
  hasLeftSidebar(layoutKey: string): boolean {
    const layout = this.layouts[layoutKey];
    return layout ? layout.elements.includes('left-sidebar') : false;
  }

  /**
   * Check if layout has right sidebar
   */
  hasRightSidebar(layoutKey: string): boolean {
    const layout = this.layouts[layoutKey];
    return layout ? layout.elements.includes('right-sidebar') : false;
  }

  /**
   * Check if layout has footer
   */
  hasFooter(layoutKey: string): boolean {
    const layout = this.layouts[layoutKey];
    return layout ? layout.elements.includes('footer') : false;
  }

  /**
   * Get layout keys for iteration
   */
  getLayoutKeys(): string[] {
    return Object.keys(this.layouts);
  }

  /**
   * Get layout button label
   */
  getLayoutLabel(layoutKey: string): string {
    const layout = this.layouts[layoutKey];
    return layout ? `${layout.key} - ${layout.title.split(' Layout')[0]}` : layoutKey.toUpperCase();
  }

  /**
   * Toggle fullscreen mode
   */
  toggleFullscreen(): void {
    this.isFullscreen = !this.isFullscreen;
  }

  /**
   * Navigate back to main application
   */
  goBack(): void {
    this.router.navigate(['/']);
  }

  /**
   * Test all layouts in sequence
   */
  testAllLayouts(): void {
    const keys = this.getLayoutKeys();
    let index = 0;
    
    const cycleLayouts = () => {
      this.selectLayout(keys[index]);
      index = (index + 1) % keys.length;
      
      if (index !== 0) {
        setTimeout(cycleLayouts, 2000); // Change layout every 2 seconds
      }
    };
    
    cycleLayouts();
  }

  /**
   * Generate random layout
   */
  randomLayout(): void {
    const keys = this.getLayoutKeys();
    const randomKey = keys[Math.floor(Math.random() * keys.length)];
    this.selectLayout(randomKey);
  }

  /**
   * Export current layout configuration
   */
  exportLayout(): void {
    const config = this.getCurrentLayout();
    const exportData = {
      layout: config,
      timestamp: new Date().toISOString(),
      selectedKey: this.selectedLayout
    };
    
    console.log('Layout Configuration:', exportData);
    
    // Create downloadable JSON file
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `layout-${this.selectedLayout}-config.json`;
    link.click();
    window.URL.revokeObjectURL(url);
  }
}
