// Shimmer Layout Demo Component Styles

.demo-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  padding: 20px;
  overflow-x: hidden;

  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    padding: 10px;
  }
}

// Header Styles
.demo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);

  .header-left {
    display: flex;
    align-items: center;
    gap: 20px;

    h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 600;
      background: linear-gradient(45deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .header-right {
    display: flex;
    gap: 12px;
  }
}

// Button Styles
.back-btn, .control-btn {
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  i {
    font-size: 16px;
  }
}

// Layout Selector Styles
.layout-selector {
  margin-bottom: 40px;

  .selector-header {
    text-align: center;
    margin-bottom: 25px;

    h2 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #ffffff;
    }

    p {
      color: rgba(255, 255, 255, 0.7);
      font-size: 16px;
    }
  }

  .layout-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    max-width: 1200px;
    margin: 0 auto;
  }
}

.layout-btn {
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }

  &.active {
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
  }

  .btn-content {
    .btn-label {
      display: block;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .btn-description {
      display: block;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.7);
      line-height: 1.4;
    }
  }
}

// Layout Preview Section
.layout-preview-section {
  margin-bottom: 40px;

  .preview-header {
    text-align: center;
    margin-bottom: 30px;

    h2 {
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 12px;
      background: linear-gradient(45deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    p {
      color: rgba(255, 255, 255, 0.8);
      font-size: 16px;
      margin-bottom: 20px;
    }

    .layout-info {
      display: flex;
      justify-content: center;
      gap: 12px;
      flex-wrap: wrap;

      .info-badge {
        padding: 6px 12px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }
    }
  }
}

.layout-preview {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.shimmer-layout-preview {
  width: 100%;
  height: 400px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

// Layout Details
.layout-details {
  margin-bottom: 40px;
  text-align: center;

  h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #ffffff;
  }

  .components-list {
    display: flex;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;

    .component-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;

      &.active {
        background: rgba(102, 126, 234, 0.2);
        border-color: rgba(102, 126, 234, 0.4);
      }

      i {
        font-size: 18px;
        color: rgba(102, 126, 234, 0.8);
      }

      span {
        font-weight: 500;
        font-size: 14px;
      }
    }
  }
}

// Code Example
.code-example {
  max-width: 800px;
  margin: 0 auto;

  h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #ffffff;
    text-align: center;
  }

  .code-block {
    background: rgba(0, 0, 0, 0.4);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow-x: auto;

    pre {
      margin: 0;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 14px;
      line-height: 1.5;
      color: #e6e6e6;

      code {
        color: #e6e6e6;

        span {
          color: rgba(102, 126, 234, 0.8);
        }
      }
    }
  }
}

// Shimmer Animation (copied from main component)
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.shimmer {
  position: relative;
  overflow: hidden;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.02) 0%,
    rgba(255, 255, 255, 0.08) 50%,
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: 8px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.4) 50%,
      transparent 100%);
    animation: shimmer 2s infinite;
  }
}

// Shimmer Grid Layouts
.shimmer-grid {
  display: grid;
  gap: 8px;
  min-height: 360px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.01);
  border-radius: 8px;

  // Layout Classes
  &.hb-layout {
    grid-template-areas: "header" "body";
    grid-template-rows: 40px 1fr;
  }

  &.hbf-layout {
    grid-template-areas: "header" "body" "footer";
    grid-template-rows: 40px 1fr 35px;
  }

  &.hlsb-layout {
    grid-template-areas: "header header" "sidebar body";
    grid-template-rows: 40px 1fr;
    grid-template-columns: 80px 1fr;
  }

  &.hlsbf-layout {
    grid-template-areas: "header header" "sidebar body" "footer footer";
    grid-template-rows: 40px 1fr 35px;
    grid-template-columns: 80px 1fr;
  }

  &.hbrs-layout {
    grid-template-areas: "header header" "body sidebar";
    grid-template-rows: 40px 1fr;
    grid-template-columns: 1fr 80px;
  }

  &.hbrsf-layout {
    grid-template-areas: "header header" "body sidebar" "footer footer";
    grid-template-rows: 40px 1fr 35px;
    grid-template-columns: 1fr 80px;
  }

  &.hlsbrs-layout {
    grid-template-areas: "header header header" "left-sidebar body right-sidebar";
    grid-template-rows: 40px 1fr;
    grid-template-columns: 70px 1fr 70px;
  }

  &.hlsbrsf-layout {
    grid-template-areas: "header header header" "left-sidebar body right-sidebar" "footer footer footer";
    grid-template-rows: 40px 1fr 35px;
    grid-template-columns: 70px 1fr 70px;
  }
}

// Shimmer Elements
.shimmer-element {
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.shimmer-header {
  grid-area: header;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
}

.shimmer-body {
  grid-area: body;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
}

.shimmer-footer {
  grid-area: footer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
}

.shimmer-left-sidebar {
  grid-area: left-sidebar;
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 8px;
}

.shimmer-right-sidebar {
  grid-area: right-sidebar;
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 8px;
}

// Shimmer Components
.shimmer-logo {
  width: 60px;
  height: 20px;
}

.shimmer-nav {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.shimmer-nav-item {
  width: 32px;
  height: 16px;
}

.shimmer-sidebar-item {
  height: 24px;

  &.small {
    height: 18px;
  }

  &.large {
    height: 32px;
  }
}

.shimmer-content-block {
  height: 32px;

  &.hero {
    height: 60px;
  }

  &.small {
    height: 24px;
  }
}

.shimmer-content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-top: 8px;
}

.shimmer-footer-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.shimmer-footer-item {
  height: 12px;

  &.wide {
    width: 60px;
  }

  &.narrow {
    width: 40px;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .demo-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;

    .header-left, .header-right {
      width: 100%;
      justify-content: center;
    }

    .header-right {
      flex-wrap: wrap;
    }
  }

  .layout-buttons {
    grid-template-columns: 1fr;
  }

  .layout-info {
    flex-direction: column;
    align-items: center;
  }

  .components-list {
    flex-direction: column;
    align-items: center;
  }

  .shimmer-layout-preview {
    height: 300px;
    padding: 12px;
  }
}
