<!-- Shimmer Layout Demo Component -->
<div class="demo-container" [class.fullscreen]="isFullscreen">
  <!-- Header -->
  <div class="demo-header">
    <div class="header-left">
      <button class="back-btn" (click)="goBack()">
        <i class="bi bi-arrow-left"></i>
        Back to App
      </button>
      <h1>Shimmer Layout Demo</h1>
    </div>
    <div class="header-right">
      <button class="control-btn" (click)="testAllLayouts()">
        <i class="bi bi-play-circle"></i>
        Test All
      </button>
      <button class="control-btn" (click)="randomLayout()">
        <i class="bi bi-shuffle"></i>
        Random
      </button>
      <button class="control-btn" (click)="exportLayout()">
        <i class="bi bi-download"></i>
        Export
      </button>
      <button class="control-btn" (click)="toggleFullscreen()">
        <i class="bi" [class.bi-fullscreen]="!isFullscreen" [class.bi-fullscreen-exit]="isFullscreen"></i>
        {{ isFullscreen ? 'Exit' : 'Fullscreen' }}
      </button>
    </div>
  </div>

  <!-- Layout Selector -->
  <div class="layout-selector">
    <div class="selector-header">
      <h2>Choose Layout Type</h2>
      <p>Select a layout to see its shimmer preview</p>
    </div>
    <div class="layout-buttons">
      <button
        *ngFor="let layoutKey of getLayoutKeys()"
        class="layout-btn"
        [class.active]="selectedLayout === layoutKey"
        (click)="selectLayout(layoutKey)">
        <div class="btn-content">
          <span class="btn-label">{{ getLayoutLabel(layoutKey) }}</span>
          <span class="btn-description">{{ layouts[layoutKey].description }}</span>
        </div>
      </button>
    </div>
  </div>

  <!-- Layout Preview -->
  <div class="layout-preview-section">
    <div class="preview-header">
      <h2>{{ getCurrentLayout().title }}</h2>
      <p>{{ getCurrentLayout().description }}</p>
      <div class="layout-info">
        <span class="info-badge">Layout Key: {{ getCurrentLayout().key }}</span>
        <span class="info-badge">Elements: {{ getCurrentLayout().elements.length }}</span>
        <span class="info-badge">CSS Class: {{ getCurrentLayout().class }}</span>
      </div>
    </div>

    <!-- Shimmer Layout Preview -->
    <div class="layout-preview">
      <div class="shimmer-layout-preview">
        <div class="shimmer-grid" [ngClass]="getCurrentLayout().class">
          <!-- Header -->
          <div class="shimmer-header shimmer-element">
            <div class="shimmer-logo shimmer"></div>
            <div class="shimmer-nav">
              <div class="shimmer-nav-item shimmer"></div>
              <div class="shimmer-nav-item shimmer"></div>
              <div class="shimmer-nav-item shimmer"></div>
            </div>
          </div>

          <!-- Left Sidebar (if applicable) -->
          <div *ngIf="hasLeftSidebar(selectedLayout)" class="shimmer-left-sidebar shimmer-element">
            <div class="shimmer-sidebar-item large shimmer"></div>
            <div class="shimmer-sidebar-item shimmer"></div>
            <div class="shimmer-sidebar-item small shimmer"></div>
            <div class="shimmer-sidebar-item shimmer"></div>
          </div>

          <!-- Body -->
          <div class="shimmer-body shimmer-element">
            <div class="shimmer-content-block hero shimmer"></div>
            <div class="shimmer-content-block shimmer"></div>
            <div class="shimmer-content-grid">
              <div class="shimmer-content-block small shimmer"></div>
              <div class="shimmer-content-block small shimmer"></div>
            </div>
          </div>

          <!-- Right Sidebar (if applicable) -->
          <div *ngIf="hasRightSidebar(selectedLayout)" class="shimmer-right-sidebar shimmer-element">
            <div class="shimmer-sidebar-item large shimmer"></div>
            <div class="shimmer-sidebar-item shimmer"></div>
            <div class="shimmer-sidebar-item small shimmer"></div>
            <div class="shimmer-sidebar-item shimmer"></div>
          </div>

          <!-- Footer (if applicable) -->
          <div *ngIf="hasFooter(selectedLayout)" class="shimmer-footer shimmer-element">
            <div class="shimmer-footer-section">
              <div class="shimmer-footer-item wide shimmer"></div>
              <div class="shimmer-footer-item narrow shimmer"></div>
            </div>
            <div class="shimmer-footer-section">
              <div class="shimmer-footer-item narrow shimmer"></div>
              <div class="shimmer-footer-item wide shimmer"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Layout Details -->
  <div class="layout-details">
    <h3>Layout Components</h3>
    <div class="components-list">
      <div
        *ngFor="let element of getCurrentLayout().elements"
        class="component-item"
        [class.active]="true">
        <i class="bi"
           [class.bi-layout-text-window-reverse]="element === 'header'"
           [class.bi-layout-sidebar]="element === 'left-sidebar'"
           [class.bi-layout-sidebar-reverse]="element === 'right-sidebar'"
           [class.bi-layout-text-window]="element === 'body'"
           [class.bi-layout-text-sidebar]="element === 'footer'"></i>
        <span>{{ element | titlecase }}</span>
      </div>
    </div>
  </div>

  <!-- Code Example -->
  <div class="code-example">
    <h3>Implementation Example</h3>
    <div class="code-block">
      <pre><code>&lt;div class="shimmer-grid {{ getCurrentLayout().class }}"&gt;
  &lt;!-- Header --&gt;
  &lt;div class="shimmer-header shimmer-element"&gt;...&lt;/div&gt;<span *ngIf="hasLeftSidebar(selectedLayout)">

  &lt;!-- Left Sidebar --&gt;
  &lt;div class="shimmer-left-sidebar shimmer-element"&gt;...&lt;/div&gt;</span>

  &lt;!-- Body --&gt;
  &lt;div class="shimmer-body shimmer-element"&gt;...&lt;/div&gt;<span *ngIf="hasRightSidebar(selectedLayout)">

  &lt;!-- Right Sidebar --&gt;
  &lt;div class="shimmer-right-sidebar shimmer-element"&gt;...&lt;/div&gt;</span><span *ngIf="hasFooter(selectedLayout)">

  &lt;!-- Footer --&gt;
  &lt;div class="shimmer-footer shimmer-element"&gt;...&lt;/div&gt;</span>
&lt;/div&gt;</code></pre>
    </div>
  </div>
</div>
