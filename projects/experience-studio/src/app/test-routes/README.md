# Test Routes

This directory contains test routes and demo components for development and testing purposes.

## Available Routes

### Shimmer Layout Demo

**URL**: `/shimmer-demo` or `/test/shimmer-layout-demo`

A comprehensive demo of the shimmer layout system that shows all 8 supported layout types with interactive previews.

#### Features:
- **Interactive Layout Selection**: Click buttons to switch between different layout types
- **Real-time Shimmer Preview**: See animated shimmer effects for each layout
- **Layout Information**: View details about each layout including components and CSS classes
- **Fullscreen Mode**: Toggle fullscreen for better viewing
- **Test All Layouts**: Automatically cycle through all layouts
- **Random Layout**: Select a random layout for testing
- **Export Configuration**: Download layout configuration as JSON

#### Supported Layouts:
1. **HB** - Header + Body
2. **HBF** - Header + Body + Footer
3. **HLSB** - Header + Left Sidebar + Body
4. **HLSBF** - Header + Left Sidebar + Body + Footer
5. **HBRS** - Header + Body + Right Sidebar
6. **HBRSF** - Header + Body + Right Sidebar + Footer
7. **HLSBRS** - Header + Left + Body + Right Sidebar
8. **HLSBRSF** - Header + Left + Body + Right + Footer

#### Usage:

1. **Navigate to the demo**:
   ```
   http://localhost:4200/shimmer-demo
   ```

2. **Test in main application**:
   - Open browser console
   - Navigate to a page with the code window component
   - Run: `component.testShimmerLayouts()`

3. **Features to test**:
   - Layout switching
   - Responsive behavior (resize window)
   - Shimmer animations
   - Fullscreen mode
   - Export functionality

#### Development Notes:

- The component is standalone and can be easily integrated into other parts of the application
- All shimmer styles are self-contained within the component
- Responsive design works on mobile and desktop
- Dark theme optimized for the existing application theme

#### File Structure:
```
test-routes/
├── shimmer-layout-demo/
│   ├── shimmer-layout-demo.component.ts
│   ├── shimmer-layout-demo.component.html
│   └── shimmer-layout-demo.component.scss
├── test-routes.module.ts
└── README.md
```

## Adding New Test Routes

To add a new test route:

1. Create a new component in the `test-routes` directory
2. Add the route to `test-routes.module.ts`
3. Update this README with documentation

## Integration with Main Application

The shimmer layout system is integrated into the main code window component at:
- `shared/components/code-window/code-window.component.html`
- `shared/components/code-window/code-window.component.ts`
- `shared/components/code-window/code-window.component.scss`

The test route provides an isolated environment for testing and demonstrating the functionality without affecting the main application flow.
