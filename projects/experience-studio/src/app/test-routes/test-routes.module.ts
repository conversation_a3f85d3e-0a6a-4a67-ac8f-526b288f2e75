import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ShimmerLayoutDemoComponent } from './shimmer-layout-demo/shimmer-layout-demo.component';

const testRoutes: Routes = [
  {
    path: 'shimmer-layout-demo',
    component: ShimmerLayoutDemoComponent,
    data: { 
      title: 'Shimmer Layout Demo',
      description: 'Interactive demo of shimmer layout system'
    }
  },
  {
    path: '',
    redirectTo: 'shimmer-layout-demo',
    pathMatch: 'full'
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(testRoutes),
    ShimmerLayoutDemoComponent
  ],
  exports: [RouterModule]
})
export class TestRoutesModule { }
